"use client"

import type React from "react"
import { useState } from "react"
import { <PERSON> } from "react-router-dom"
import toast from "react-hot-toast"
import { Edit, Trash2, BookO<PERSON>, Plus } from "lucide-react"

import { useGetBooksQuery, useDeleteBookMutation } from "../app/api/apiSlice"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"


interface Book {
  _id: string
  title: string
  author: string
  genre: string
  isbn: string
  copies: number
  available: boolean
  availableCopies: number
}

const BookList: React.FC = () => {
  const [itemsToShow, setItemsToShow] = useState(20)
  const [showMockData, setShowMockData] = useState(false)
  const { data, isLoading, isError, error } = useGetBooksQuery({ filter: itemsToShow })
  const [deleteBook] = useDeleteBookMutation()

  // Mock data for testing pagination
  const generateMockBooks = (count: number): Book[] => {
    const mockBooks: Book[] = []
    for (let i = 1; i <= count; i++) {
      mockBooks.push({
        _id: `mock-${i}`,
        title: `Mock Book ${i}`,
        author: `Author ${i}`,
        genre: 'FICTION',
        isbn: `978-0-000-${i.toString().padStart(5, '0')}-0`,
        copies: Math.floor(Math.random() * 10) + 1,
        available: Math.random() > 0.3,
        availableCopies: Math.floor(Math.random() * 5) + 1
      })
    }
    return mockBooks
  }

  const handleDelete = async (id: string) => {
    try {
      await deleteBook(id).unwrap()
      toast.success("Book deleted successfully!")
    } catch (err: unknown) {
      const errorMessage =
        err &&
        typeof err === "object" &&
        "data" in err &&
        err.data &&
        typeof err.data === "object" &&
        "message" in err.data
          ? (err.data as { message: string }).message
          : err instanceof Error
            ? err.message
            : "Unknown error"
      toast.error(`Failed to delete book: ${errorMessage}`)
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-3xl font-bold text-center">Library Books</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex space-x-4">
                <Skeleton className="h-4 w-[200px]" />
                <Skeleton className="h-4 w-[150px]" />
                <Skeleton className="h-4 w-[100px]" />
                <Skeleton className="h-4 w-[120px]" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (isError) {
    const errorMessage =
      error && "data" in error && error.data && typeof error.data === "object" && "message" in error.data
        ? (error.data as { message: string }).message
        : "Please try again later."

    return (
      <Card>
        <CardContent className="pt-6">
          <Alert variant="destructive">
            <AlertDescription>
              <div className="font-semibold">Error loading books!</div>
              <div>{errorMessage}</div>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  // Use mock data if enabled, otherwise use real API data
  const realBooks: Book[] = data?.data || []
  const mockBooks = showMockData ? generateMockBooks(itemsToShow) : []
  const books: Book[] = showMockData ? mockBooks : realBooks
  const totalBooks = books.length

  // Create pagination options for different amounts of data
  // Only show options that make sense based on available data
  const paginationOptions = [5, 10, 15, 20, 30].filter(option => option <= Math.max(totalBooks, 10))

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-3xl font-bold">Library Books</CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              variant={showMockData ? "default" : "outline"}
              size="sm"
              onClick={() => setShowMockData(!showMockData)}
            >
              {showMockData ? "Show Real Data" : "Test with Mock Data"}
            </Button>
            <Button asChild>
              <Link to="/create-book" className="flex items-center">
                <Plus className="mr-2 h-4 w-4" />
                Add New Book
              </Link>
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Author</TableHead>
                <TableHead>Genre</TableHead>
                <TableHead>ISBN</TableHead>
                <TableHead>Total Copies</TableHead>
                <TableHead>Available</TableHead>
                <TableHead className="text-center">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {books.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                    No books found in the library.
                  </TableCell>
                </TableRow>
              ) : (
                books.map((book) => (
                  <TableRow key={book._id}>
                    <TableCell className="font-medium">{book.title}</TableCell>
                    <TableCell>{book.author}</TableCell>
                    <TableCell>{book.genre}</TableCell>
                    <TableCell className="font-mono text-sm">{book.isbn}</TableCell>
                    <TableCell>{book.copies}</TableCell>
                    <TableCell>
                      <Badge variant={book.available ? "default" : "secondary"}>
                        {book.available ? "Available" : "Unavailable"} ({book.availableCopies})
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center justify-center space-x-2">
                        <Button variant="ghost" size="sm" asChild>
                          <Link to={`/edit-book/${book._id}`}>
                            <Edit className="h-4 w-4" />
                            <span className="sr-only">Edit book</span>
                          </Link>
                        </Button>

                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <Trash2 className="h-4 w-4 text-destructive" />
                              <span className="sr-only">Delete book</span>
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                              <AlertDialogDescription>
                                This action cannot be undone. This will permanently delete the book "{book.title}" from
                                the library.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDelete(book._id)}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                              >
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>

                        <Button
                          variant="ghost"
                          size="sm"
                          asChild
                          className={!book.available ? "opacity-50 cursor-not-allowed" : ""}
                        >
                          <Link to={`/borrow/${book._id}`} onClick={(e) => !book.available && e.preventDefault()}>
                            <BookOpen className="h-4 w-4" />
                            <span className="sr-only">
                              {!book.available ? "No copies available to borrow" : "Borrow book"}
                            </span>
                          </Link>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination - Show different amounts of data */}
        <div className="mt-6">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            <div className="text-sm text-muted-foreground">
              Showing {totalBooks} books total
              {itemsToShow < totalBooks && (
                <span className="text-orange-600 ml-2">
                  (Requested {itemsToShow}, but only {totalBooks} available)
                </span>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">Request:</span>
              {paginationOptions.map((option) => (
                <Button
                  key={option}
                  variant={itemsToShow === option ? "default" : "outline"}
                  size="sm"
                  onClick={() => setItemsToShow(option)}
                  className="min-w-[50px]"
                >
                  {option}
                </Button>
              ))}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setItemsToShow(Math.min(itemsToShow + 5, 50))}
                className="ml-2"
                disabled={itemsToShow >= 50}
              >
                +5 More
              </Button>
            </div>
          </div>
          {totalBooks < itemsToShow && (
            <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-md">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                💡 <strong>Note:</strong> You requested {itemsToShow} books, but your database only contains {totalBooks} books.
                To test pagination properly, add more books to your database.
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default BookList
